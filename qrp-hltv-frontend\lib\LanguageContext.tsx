import React, { createContext, useContext, useState, useEffect } from 'react';

type Language = 'ru' | 'gb';

interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: string) => string;
}

const translations = {
  ru: {
    news: 'Новости',
    tournaments: 'Турниры',
    players: 'Пилоты',
    teams: 'Команды',
    partners: 'Партнеры',
    latestNews: 'Последние новости',
    home_page_title: 'QRP HLTV',
    news_page_title: 'Новости',
    admin_support_requests_title: 'Заявки поддержки',
    qualifier: 'Квалификация:',
    status_upcoming: 'Скоро',
    status_finished: 'Завершен',
    tournaments_page_title: 'Турниры',
    qualifiers_label: 'Квалификация:',
    tandems_label: 'Парные:',
    date_tba: 'TBA',
    description_label: 'Описание',
    status_ongoing: 'Идет',
    players_page_title: 'Пилоты',
    no_image_label: 'Нет изображения',
    elo_label: 'ELO:',
    teams_page_title: 'Команды',
    no_logo_label: 'Нет логотипа',
    players_label: 'Игроки:',
    partners_page_title: 'Партнеры',
    visit_website_button: 'Посетить сайт',
    real_name_label: 'Имя:',
    country_label: 'Страна:',
    team_label: 'Команда:',
    city_label: 'Город:',
    age_label: 'Возраст:',
    discord_label: 'Discord:',
    copy_discord_tip: 'Скопировать Discord',
    search_nickname_placeholder: 'Поиск по нику',
    search_team_placeholder: 'Поиск по названию команды',
    toast_copy_success: 'Тег скопирован в буфер обмена!',
    toast_copy_failed: 'Не удалось скопировать тег.',
    team_players_label: 'Игроки команды',
    no_players_label: 'В команде нет игроков',
    no_elo_data_label: 'Нет данных ELO',
    contact: 'Поддержка',
    contact_page_title: 'Связаться с нами',
    contact_simple_message: 'Хотите попасть на сайт, сменили команду или просто есть вопрос - пишите нам здесь или в Discord',
    contact_form_name_label: 'Имя',
    contact_form_discord_label: 'Discord тег',
    contact_form_subject_label: 'Тема',
    contact_form_message_label: 'Сообщение',
    contact_form_name_placeholder: 'Ваше имя',
    contact_form_discord_placeholder: '@username',
    contact_form_subject_placeholder: 'Чем мы можем помочь?',
    contact_form_message_placeholder: 'Пожалуйста, опишите подробно ваш вопрос или проблему...',
    contact_form_submit_button: 'Отправить сообщение',
    contact_form_submitting: 'Отправка...',
    contact_form_success_title: 'Сообщение отправлено успешно!',
    contact_form_success_message: 'Мы свяжемся с вами как можно скорее.',
    contact_form_error_title: 'Ошибка отправки сообщения',
    contact_form_error_message: 'Пожалуйста, попробуйте позже или свяжитесь с нами напрямую.',
    contact_form_name_required: 'Имя обязательно',
    contact_form_discord_required: 'Discord тег обязателен',
    contact_form_discord_invalid: 'Пожалуйста, введите корректный Discord тег (например: @username)',
    contact_form_subject_required: 'Тема обязательна',
    contact_form_message_required: 'Сообщение обязательно',
    contact_form_message_too_short: 'Сообщение должно содержать минимум 10 символов',
    contact_form_title: 'Отправить сообщение',
    highlights_title: 'Что нового?',
    highlights_previous: 'Предыдущий',
    highlights_next: 'Следующий',
    highlights_slide_indicator: 'Слайд',
    admin_page_title: 'Админка',
    admin_accessing: 'Доступ к админке...',
    admin_permission_granted: 'Доступ разрешен.',
    admin_welcome: 'Добро пожаловать, Envy',
    admin_built_by: 'Создано и поддерживается',
    admin_tech_stack: 'Технологии',
    admin_contact: 'Контакты',
    admin_contact_description: 'Обращайтесь по вопросам разработки или возможностям сотрудничества.',
    admin_prank_title: 'Вас разыграли!',
    admin_prank_message: 'Админки тут, конечно, нет, но раз уж вы, мой любопытный друг, заглянули, то вот немного обо мне:',
    admin_role_fullstack: 'Фулстек-разработчик',
    admin_role_designer: 'UI/UX дизайнер',
    admin_role_enthusiast: 'Крутой парень',
    admin_developer_title: 'Разработчик и мейнтейнер QRP HLTV и CarX Hub',
    footer_brand: 'QRP HLTV • CarX Drift Racing',
    footer_made_with_love: 'Сделано с ❤️ для сообщества CarX',
    teams_elo_toggle_label: 'Включить всех игроков в расчет ELO команды',
    teams_unranked_tab: 'Команды вне зачета',
    teams_unranked_explanation: 'Команды с менее чем 3 игроками не учитываются при расчете ELO по топ-3',
    pilots_notice_title: ' Обращение ко всем пилотам!',
    pilots_notice_text: 'Если вы есть в списке пилотов, то участвуйте в турнирах под ником, под которым вы и указаны. Это ускоряет обработку результатов, облегчает работу администрации, и гарантирует что ваши очки засчитают корректно. Надеемся на ваше понимание.',
  },
  gb: {
    news: 'News',
    tournaments: 'Tournaments',
    players: 'Pilots',
    teams: 'Teams',
    partners: 'Partners',
    latestNews: 'Latest News',
    home_page_title: 'QRP HLTV',
    news_page_title: 'News',
    admin_support_requests_title: 'Support Requests',
    qualifier: 'Qual:',
    status_upcoming: 'Upcoming',
    status_finished: 'Finished',
    tournaments_page_title: 'Tournaments',
    qualifiers_label: 'Qualifiers:',
    tandems_label: 'Tandems:',
    date_tba: 'TBA',
    description_label: 'Description',
    status_ongoing: 'Ongoing',
    players_page_title: 'Pilots',
    no_image_label: 'No Image',
    elo_label: 'ELO:',
    teams_page_title: 'Teams',
    no_logo_label: 'No Logo',
    players_label: 'Players:',
    partners_page_title: 'Partners',
    visit_website_button: 'Visit website',
    real_name_label: 'Name:',
    country_label: 'Country:',
    team_label: 'Team:',
    city_label: 'City:',
    age_label: 'Age:',
    discord_label: 'Discord:',
    copy_discord_tip: 'Copy Discord Tag',
    search_nickname_placeholder: 'Search by nickname',
    search_team_placeholder: 'Search by team name',
    toast_copy_success: 'Discord tag copied to clipboard!',
    toast_copy_failed: 'Failed to copy Discord tag.',
    team_players_label: 'Team Players',
    no_players_label: 'No players in this team',
    no_elo_data_label: 'No ELO data available',
    contact: 'Support',
    contact_page_title: 'Contact Us',
    contact_simple_message: 'Do you want to be featured on the site, have changed team or just want inquiry - write to us here or on Discord',
    contact_form_name_label: 'Name',
    contact_form_discord_label: 'Discord Tag',
    contact_form_subject_label: 'Subject',
    contact_form_message_label: 'Message',
    contact_form_name_placeholder: 'Your full name',
    contact_form_discord_placeholder: '@username',
    contact_form_subject_placeholder: 'What can we help you with?',
    contact_form_message_placeholder: 'Please describe your question or issue in detail...',
    contact_form_submit_button: 'Send Message',
    contact_form_submitting: 'Sending...',
    contact_form_success_title: 'Message sent successfully!',
    contact_form_success_message: 'We\'ll get back to you as soon as possible.',
    contact_form_error_title: 'Error sending message',
    contact_form_error_message: 'Please try again later or contact us directly.',
    contact_form_name_required: 'Name is required',
    contact_form_discord_required: 'Discord tag is required',
    contact_form_discord_invalid: 'Please enter a valid Discord tag (e.g., @username)',
    contact_form_subject_required: 'Subject is required',
    contact_form_message_required: 'Message is required',
    contact_form_message_too_short: 'Message must be at least 10 characters long',
    contact_form_title: 'Send us a Message',
    highlights_title: 'What’s new?',
    highlights_previous: 'Previous',
    highlights_next: 'Next',
    highlights_slide_indicator: 'Slide',
    admin_page_title: 'Admin Panel',
    admin_accessing: 'Accessing admin panel...',
    admin_permission_granted: 'Permission granted.',
    admin_welcome: 'Welcome, Envy',
    admin_built_by: 'Built & Maintained by',
    admin_tech_stack: 'Tech Stack',
    admin_contact: 'Contacts',
    admin_contact_description: 'Feel free to reach out for development questions or collaboration opportunities.',
    admin_prank_title: 'You\'ve been pranked!',
    admin_prank_message: "Of course, there's no admin panel here. Welcome, my nosy friend! Since you're here, here's some info about me:",
    admin_role_fullstack: 'Full-Stack Developer',
    admin_role_designer: 'UI/UX Designer',
    admin_role_enthusiast: 'Chill guy',
    admin_developer_title: 'Developer & Maintainer of QRP HLTV and CarX Hub',
    footer_brand: 'QRP HLTV • CarX Drift Racing',
    footer_made_with_love: 'Made with ❤️ for the CarX community',
    teams_elo_toggle_label: 'Include all players in team ELO calculation',
    teams_unranked_tab: 'Ineligible for Ranking',
    teams_unranked_explanation: 'Teams with fewer than 3 players cannot be ranked using top-3 ELO calculation',
    pilots_notice_title: ' Notice to all pilots!',
    pilots_notice_text: 'If you are registered on the site, please participate in tournaments using the nickname you are registered under. This speeds up results processing, makes administration easier, and ensures your points are counted correctly. We appreciate your understanding.',
  },
};

const defaultLanguage: Language = 'ru';

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export function LanguageProvider({ children }: { children: React.ReactNode }) {
  const [language, setLanguage] = useState<Language>(defaultLanguage);

  useEffect(() => {
    const savedLang = localStorage.getItem('language');
    if (savedLang && (savedLang === 'ru' || savedLang === 'gb')) {
      setLanguage(savedLang);
    }
  }, []);

  const t = (key: string): string => {
    const currentTranslations = translations[language] || translations[defaultLanguage];
    return currentTranslations[key as keyof typeof translations['gb']] || key;
  };

  const value = {
    language,
    setLanguage: (lang: Language) => {
      if (lang === 'ru' || lang === 'gb') {
        setLanguage(lang);
        localStorage.setItem('language', lang);
      }
    },
    t,
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
}

export function useLanguage() {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
}